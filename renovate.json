{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "baseBranches": ["main"], "extends": ["config:recommended"], "assignees": ["m<PERSON><PERSON>", "GordonY", "jason.le"], "prHourlyLimit": 15, "dependencyDashboard": true, "labels": ["dependencies"], "packageRules": [{"matchUpdateTypes": "major", "addLabels": ["major"]}, {"matchUpdateTypes": "minor", "addLabels": ["minor"], "schedule": ["every weekend"]}, {"matchUpdateTypes": "patch", "addLabels": ["patch"], "schedule": ["every weekend"]}, {"matchCategories": "js", "addLabels": ["javascript"]}, {"matchCategories": "python", "addLabels": ["python"]}, {"matchCategories": "terraform", "groupName": "Terraform dependencies", "addLabels": ["terraform"]}, {"matchPackageNames": ["botocore", "boto3", "boto", "s3transfer"], "groupName": "AWS SDK dependencies", "addLabels": ["aws-sdk"]}]}