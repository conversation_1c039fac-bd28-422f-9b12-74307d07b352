const lambdaFunction = require('./app');
const SECONDS = 1000;
jest.setTimeout(10 * SECONDS)

it('App::Handler lambda handler returns the input as the output', async () => {
    const result = await lambdaFunction.handler(event);
    expect(result).toBe(event);
});

const event = {
    "lambda": "invoice-generation",
    "uuid": "4c059f9b-3ae6-11ee-bc1f-e1ea9256912e",
    "account": "bd7ef3f5-4a3f-4645-9a8d-fd3747b3ffae",
    "record": {
        "account_number": {
            "S": "**************"
        },
        "adjustments": {
            "M": {
                "summary": {
                    "M": {
                        "total": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "amount": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "tax": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        }
                    }
                },
                "items": {
                    "L": []
                }
            }
        },
        "payments": {
            "M": {
                "summary": {
                    "M": {
                        "total": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "amount": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "tax": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        }
                    }
                },
                "items": {
                    "L": []
                }
            }
        },
        "invoice_time": {
            "S": "05:05:30"
        },
        "template_path": {
            "S": "base"
        },
        "billing_address": {
            "S": "Jalan Tuanku Abdul Rahman, Chow Kit, Kuala Lumpur, Wilayah Persekutuan Kuala Lumpur"
        },
        "uuid": {
            "S": "4c059f9b-3ae6-11ee-bc1f-e1ea9256912e"
        },
        "invoice_date": {
            "S": "15/08/2023"
        },
        "leases": {
            "M": {
                "summary": {
                    "M": {
                        "total": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "amount": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "tax": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        }
                    }
                },
                "items": {
                    "L": []
                }
            }
        },
        "po_number": {
            "S": ""
        },
        "balance": {
            "M": {
                "closing": {
                    "S": "0.00"
                },
                "accrued": {
                    "M": {
                        "i18n": {
                            "NULL": true
                        },
                        "base": {
                            "S": "0.00"
                        }
                    }
                },
                "opening": {
                    "S": "0.00"
                }
            }
        },
        "to_date": {
            "S": "01/08/2023"
        },
        "additional_charges": {
            "M": {
                "summary": {
                    "M": {
                        "total": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "amount": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "tax": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        }
                    }
                },
                "items": {
                    "L": []
                }
            }
        },
        "contact": {
            "M": {
                "phone": {
                    "S": "+65 3 125 2443"
                },
                "portal": {
                    "S": "myportal.symbionetworks.com"
                },
                "email": {
                    "S": "<EMAIL>"
                }
            }
        },
        "invoice_number": {
            "S": "MYCO00000037949"
        },
        "info": {
            "M": {
                "currency": {
                    "M": {
                        "i18n": {
                            "S": "MYR"
                        },
                        "base": {
                            "S": "MYR"
                        }
                    }
                },
                "taxation": {
                    "S": "SST"
                }
            }
        },
        "outbound_calls": {
            "M": {
                "summary": {
                    "M": {
                        "total": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "amount": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "tax": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        }
                    }
                },
                "items": {
                    "L": []
                },
                "statistics": {
                    "M": {
                        "duration": {
                            "S": "0.00"
                        },
                        "total": {
                            "S": "0"
                        },
                        "answered": {
                            "S": "0"
                        }
                    }
                }
            }
        },
        "from_date": {
            "S": "01/08/2023"
        },
        "inbound_calls": {
            "M": {
                "summary": {
                    "M": {
                        "total": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "amount": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        },
                        "tax": {
                            "M": {
                                "i18n": {
                                    "NULL": true
                                },
                                "base": {
                                    "S": "0.00"
                                }
                            }
                        }
                    }
                },
                "items": {
                    "L": []
                },
                "statistics": {
                    "M": {
                        "duration": {
                            "S": "0.00"
                        },
                        "total": {
                            "S": "0"
                        },
                        "answered": {
                            "S": "0"
                        }
                    }
                }
            }
        },
        "tax_exempt": {
            "BOOL": false
        },
        "billing_country": {
            "S": "Malaysia"
        },
        "due_date": {
            "S": "29/08/2023"
        },
        "account_id": {
            "S": "bd7ef3f5-4a3f-4645-9a8d-fd3747b3ffae"
        },
        "filename": {
            "S": "Invoice_New_Account_MYCO00000037949"
        },
        "billing_postcode": {
            "S": ""
        },
        "name": {
            "S": "ABC carrier"
        },
        "invoice_date_generated": {
            "S": "15/08/2023"
        }
    },
    "key": "bd7ef3f5-4a3f-4645-9a8d-fd3747b3ffae/4c059f9b-3ae6-11ee-bc1f-e1ea9256912e.docx",
    "bucket": "billing-invoice-service-uat-my-s3",
    "readable_filename": "Invoice_New_Account_MYCO00000037949"
};