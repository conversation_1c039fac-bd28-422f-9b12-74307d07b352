const {convertTo} = require("@shelf/aws-lambda-libreoffice");
const fs = require("fs").promises;
const AWS = require("aws-sdk");
const path = require("path");

const SHARED_INVOICE_FOLDER = "invoice-stream";
const s3 = new AWS.S3({apiVersion: "2006-03-01"});
const retrieve = async (params) => await s3.getObject(params).promise();
const upload = async (params) => await s3.putObject(params).promise()

exports.handler = async (event) => {
    console.log(`Received event: ${JSON.stringify(event, null, 2)}`);
    const body = event;
    console.log(`body: ${JSON.stringify(body, null, 2)}`);

    body["lambda"] = "pdf-generation";

    // Get the object from the event and show its content type
    // const bucket = event.Records[0].s3.bucket.name;
    // const key = decodeURIComponent(event.Records[0].s3.object.key.replace(/\+/g, ' '));
    const bucket = event.bucket;
    const key = decodeURIComponent(event.key.replace(/\+/g, ' '));
    const readable_filename = event.readable_filename;

    try {
        console.log(`Retrieving file from bucket: ${bucket} and key: ${key}`);
        const docx = await retrieve({Bucket: bucket, Key: key});
        console.log(`File from S3 to be converted: ${docx}`);
        const [folder, filename, ...remainder] = key.split("/");
        console.log(`Folder: ${folder} and filename ${filename} extracted from key`);
        const tmpdir = path.join('/tmp', filename);
        console.log(`Output filepath: ${tmpdir}`);
        console.log(`Readable filename: ${readable_filename}`)

        // Convert docx file to pdf
        await fs.writeFile(tmpdir, docx.Body);
        const convertedFilePath = await convertTo(filename, "pdf");
        console.log(`Converted filepath: ${convertedFilePath}`);
        const fileData = await fs.readFile(convertedFilePath);
        const base64Data = Buffer.from(fileData, "binary");
        const [pdf] = convertedFilePath.split("/").slice(-1);
        console.log(`PDF name: ${pdf}`);
        const account_target = folder + "/" + pdf;
        const global_target = SHARED_INVOICE_FOLDER + "/" + readable_filename + ".pdf";

        body["key"] = account_target;

        // Upload converted file to S3 bucket
        const targets = [account_target, global_target];
        console.log(`Uploading PDF to: ${targets}`)
        const upload_params = { Body: base64Data, Bucket: bucket };
        const uploads = targets.map(target => upload({ ...upload_params, Key: target }));
        // Await all uploads and throw if any fail
        await Promise.all(uploads);
        console.log(`Uploaded PDF to: ${targets}`);
    } catch (error) {
        console.log(`ERROR : ${error}`);
        body["error"] = error;  //do not change key 'error' as the existence of this key is used as a filter in the state machine
    }

    console.log(`end body: ${JSON.stringify(body, null, 2)}`);
    return body;
};