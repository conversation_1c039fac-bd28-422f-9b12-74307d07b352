import os
import boto3
import json
import traceback


def handler(event, _):
    body = {'lambda': 'step-function'}

    try:
        print(f'Received event: {event}')
        state_machine_arn = os.environ['STATE_MACHINE_ARN']

        print(f'State machine: {state_machine_arn}')

        for record in event['Records']:
            dynamo_record = record['dynamodb']['NewImage']
            print(f'dynamo_record: {dynamo_record}')

            context = deserialize_dynamo_to_python(dynamo_record)
            print(f'context: {context}')

            body['uuid'] = context['uuid']
            body['account'] = context['account_id']
            body['record'] = dynamo_record

            sf = boto3.client('stepfunctions')
            response = sf.start_execution(
                stateMachineArn=state_machine_arn,
                input=json.dumps(body)
            )

            print(f'Response: {response}')

        json_body = json.dumps(body)
        print(f'json_body: {json_body}')

    except Exception as e:
        print(f'An exception occurred: {e}')
        # printing stack trace
        traceback.print_exc()
        # do not change key 'error' as the existence of this key is used as a filter in the state machine
        body['error'] = str(e)
        json_body = json.dumps(body)
        return json_body

    return json_body


# handles translation from dynamo record to ordinary json key: value structure
def deserialize_dynamo_to_python(low_level_data):
    # Lazy-eval the dynamodb attribute (boto3 is dynamic!)
    boto3.resource('dynamodb')

    # To go from low-level format to python
    deserializer = boto3.dynamodb.types.TypeDeserializer()
    python_data = {k: deserializer.deserialize(v) for k, v in low_level_data.items()}
    return python_data


if __name__ == '__main__':
    handler("", "")
