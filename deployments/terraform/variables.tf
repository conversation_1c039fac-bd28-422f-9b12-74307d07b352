variable "name" {
  default = "invoice-generation"
}

variable "remote_state_bucket" {
  description = "Global remote state bucket, same for all projects"
  default     = "mnfgroup-terraform-remotestate"
}

variable "remote_state_region" {
  description = "Global remote state s3 bucket region, same for all projects"
  default     = "ap-southeast-2"
}

variable "networking_remote_state_key" {
  description = "The project_key defined in the REMOTE project with which you wish to pull the data"
  default     = "networking/tfstate"
}

variable "security_group_state_key" {
  description = "The project_key defined in the REMOTE project with which you wish to pull the data"
  default     = "security_groups/tfstate"
}

variable "aws_region"{
  type = string
  description = "The region of aws infra"
  default = "ap-southeast-1"
}

variable "remote_workspace" {
  type = string
  description = "Terraform remote workspace"
}

variable "country" {
  default = "my"
  description = "Country variable for different regions for deployment"
}

variable "commit_ref" {
  type        = string
  description = "The commit ref name"
}

variable "project_name" {
  type        = string
  description = "The project name specified"
  default = "generation"
}

variable "gitlab_runner_ingress_ips" {
  type = list(string)
  description = "Gitlab runner ingress IPs for RDS security group"
}

variable "step_function_lambda_root" {
  type        = string
  description = "The relative path to the source of the lambda"
  default     = "../../step-function/app"
}

variable "invoice_lambda_root" {
  type        = string
  description = "The relative path to the source of the lambda"
  default     = "../../invoice-generation/app"
}

variable "invoice_layers_root" {
  type        = string
  description = "The relative path to the lambda layer dependencies"
  default     = "../../invoice-generation/layers"
}

variable "pdf_generation_working_dir" {
  type        = string
  description = "The relative path to the source of the lambda"
  default     = "../../pdf-generation/"
}

variable "commit_tag" {
  type = string
  description = "The commit tag"
}
