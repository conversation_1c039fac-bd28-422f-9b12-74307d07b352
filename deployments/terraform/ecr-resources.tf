resource aws_ecr_repository repo {
  name         = local.ecr_repository_name
  force_delete = "true"

  tags = merge(
    local.common_tags,
    { "Name": "${local.prefix}-pdf-generation-ecr-repo" }
  )
}

# The null_resource resource implements the standard resource lifecycle
# but takes no further action.

# The triggers argument allows specifying an arbitrary set of values that,
# when changed, will cause the resource to be replaced.

resource null_resource ecr_image {
  depends_on = [
    aws_ecr_repository.repo
  ]
  triggers = {
#    rerun_every_time = uuid()
    docker_file       = md5(file("${path.module}/../../pdf-generation/Dockerfile"))
    app_file          = md5(file("${path.module}/../../pdf-generation/app/app.js"))
    package_file      = md5(file("${path.module}/../../pdf-generation/app/package.json"))
    package_lock_file = md5(file("${path.module}/../../pdf-generation/app/package-lock.json"))
  }

  # The local-exec provisioner invokes a local executable after a resource is created.
  # This invokes a process on the machine running Terraform, not on the resource.
  # path.module: the filesystem path of the module where the expression is placed.

  provisioner "local-exec" {
    command = <<EOF
        aws ecr get-login-password --region ${var.aws_region} | docker login --username AWS --password-stdin ${local.account_id}.dkr.ecr.${var.aws_region}.amazonaws.com
        docker build --load -t ${aws_ecr_repository.repo.repository_url}:${local.ecr_image_tag} .
        docker image ls
        docker push ${aws_ecr_repository.repo.repository_url}:${local.ecr_image_tag}
      EOF
    interpreter = ["bash", "-c"]
    working_dir = var.pdf_generation_working_dir
  }
}

data aws_ecr_image lambda_image {
  depends_on = [
    null_resource.ecr_image
  ]
  repository_name = local.ecr_repository_name
  image_tag       = local.ecr_image_tag
}