#####################################################
# INVOICE GENERATION LAMBDA
#####################################################
resource "aws_lambda_function" "invoice_lambda" {
  function_name    = replace("${substr(local.prefix, 0, 49)}-invoice-lambda", "--", "-")
  filename         = data.archive_file.invoice_lambda_source.output_path
  source_code_hash = data.archive_file.invoice_lambda_source.output_base64sha256
  role             = aws_iam_role.role.arn
  handler          = "app.handler"
  runtime          = "python3.12"
  timeout          = "240"
  # 1,769MB is equivalent to 1 vCPU as per AWS documentation: https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html
  memory_size      = "1769"
  layers           = [aws_lambda_layer_version.docxtpl_lambda_layer.arn]

  tags = merge(
    local.common_tags,
    { "Name": "${local.prefix}-invoice-lambda" }
  )

  tracing_config {
    mode = "Active"
  }

  environment {
    variables = {
      INVOICE_STORAGE_BUCKET_NAME   = data.terraform_remote_state.invoice_service.outputs.invoice_storage_bucket_name
      TEMPLATE_BUCKET_NAME          = data.terraform_remote_state.template_service.outputs.template_bucket_name
    }
  }
}

resource "random_uuid" "invoice_lambda_src_hash" {
  keepers = {
    for filename in setunion(
      fileset(var.invoice_lambda_root, "app.py"),
    ) :
    filename => filemd5("${var.invoice_lambda_root}/${filename}")
  }
}

data "archive_file" "invoice_lambda_source" {
  excludes   = [
    "__pycache__",
    "venv",
  ]

  source_dir  = var.invoice_lambda_root
  output_path = "${random_uuid.invoice_lambda_src_hash.result}.zip"
  type        = "zip"
}

resource "aws_lambda_layer_version" "docxtpl_lambda_layer" {
  filename            = "${var.invoice_layers_root}/docxtpl-layer.zip"
  layer_name          = "${local.prefix}-lambda-layer"
  source_code_hash    = filebase64sha256("${var.invoice_layers_root}/docxtpl-layer.zip")
  compatible_runtimes = ["python3.12"]
}


#####################################################
# PDF GENERATION LAMBDA
#####################################################
resource "aws_lambda_function" "pdf_lambda" {
  depends_on        = [null_resource.ecr_image]
  function_name     = replace("${substr(local.prefix, 0, 53)}-pdf-lambda", "--", "-")
  image_uri         = "${aws_ecr_repository.repo.repository_url}@${data.aws_ecr_image.lambda_image.id}"
  package_type      = "Image"
  publish           = "true"
  role              = aws_iam_role.role.arn

  timeout          = "240"
  # 1,769MB is equivalent to 1 vCPU as per AWS documentation: https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html
  memory_size      = "1769"

  tags = merge(
    local.common_tags,
    { "Name": "${local.prefix}-pdf-lambda" }
  )

  # AWS X-Ray
  tracing_config {
    mode = "Active"
  }
}

#####################################################
# STEP FUNCTION LAMBDA
#####################################################
resource "aws_lambda_function" "sfn_lambda" {
  depends_on = [ aws_sfn_state_machine.sfn_state_machine ]
  function_name    = replace("${substr(local.prefix, 0, 53)}-sfn-lambda", "--", "-")
  filename         = data.archive_file.step_function_lambda_source.output_path
  source_code_hash = data.archive_file.step_function_lambda_source.output_base64sha256
  role             = aws_iam_role.sfn_lambda_role.arn
  handler          = "app.handler"
  runtime          = "python3.12"
  timeout          = "240"
  # 1,769MB is equivalent to 1 vCPU as per AWS documentation: https://docs.aws.amazon.com/lambda/latest/dg/gettingstarted-limits.html
  memory_size      = "1769"

  tags = merge(
    local.common_tags,
    { "Name": "${local.prefix}-sfn-lambda" }
  )

  tracing_config {
    mode = "Active"
  }

  environment {
    variables = {
      STATE_MACHINE_ARN   = aws_sfn_state_machine.sfn_state_machine.arn
    }
  }
}

resource "aws_lambda_event_source_mapping" "event_source_mapping_invoice_summarised" {
  event_source_arn  = data.terraform_remote_state.invoice_service.outputs.dynamodb_summarised_stream_arn
  function_name     = aws_lambda_function.sfn_lambda.arn
  starting_position = "LATEST"
}

resource "random_uuid" "step_function_lambda_src_hash" {
  keepers = {
    for filename in setunion(
      fileset(var.step_function_lambda_root, "app.py"),
    ) :
    filename => filemd5("${var.step_function_lambda_root}/${filename}")
  }
}

data "archive_file" "step_function_lambda_source" {
  excludes   = [
    "__pycache__",
    "venv",
  ]

  source_dir  = var.step_function_lambda_root
  output_path = "${random_uuid.step_function_lambda_src_hash.result}.zip"
  type        = "zip"
}


