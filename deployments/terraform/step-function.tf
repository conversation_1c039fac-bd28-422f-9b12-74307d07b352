resource "aws_iam_role" "step_function_role" {
  name = replace("${substr(local.prefix, 0, 55)}-sfn-role", "--", "-")

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "states.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_policy" "state_machine_policy" {
  name    = "${local.prefix}-state-machine-policy"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "1",
            "Effect": "Allow",
            "Action": [
              "states:ListStateMachines",
              "states:ListActivities",
              "states:CreateStateMachine",
              "states:CreateActivity"
            ],
            "Resource": "*"
        }
    ]
}
EOF

}

resource "aws_iam_policy" "invoke_lambda_policy" {
  name    = "${local.prefix}-invoke-lambda-policy"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "1",
            "Effect": "Allow",
            "Action": [
                "lambda:InvokeFunction"
            ],
            "Resource": "*"
        }
    ]
}
EOF

}

resource "aws_iam_policy" "dynamodb_policy" {
  name    = "${local.prefix}-dynamodb-policy"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "1",
            "Effect": "Allow",
            "Action": [
                "dynamodb:PutItem",
                "dynamodb:GetItem",
                "dynamodb:UpdateItem",
                "dynamodb:DeleteItem"
            ],
            "Resource": "*"
        }
    ]
}
EOF

}

resource "aws_iam_role_policy_attachment" "sfn-role-state-machine-policy-attach" {
  role      = aws_iam_role.step_function_role.name
  policy_arn = aws_iam_policy.state_machine_policy.arn
}

resource "aws_iam_role_policy_attachment" "sfn-role-invoke-lambda-policy-attach" {
  role       = aws_iam_role.step_function_role.name
  policy_arn = aws_iam_policy.invoke_lambda_policy.arn
}

resource "aws_iam_role_policy_attachment" "sfn-role-dynamodb-policy-attach" {
  role       = aws_iam_role.step_function_role.name
  policy_arn = aws_iam_policy.dynamodb_policy.arn
}

resource "aws_sfn_state_machine" "sfn_state_machine" {
  name     = replace("${substr(local.prefix, 0, 62)}-sfn-state-machine", "--", "-")
  role_arn = aws_iam_role.step_function_role.arn

  tags = merge(
    local.common_tags,
    { "Name": "${local.prefix}-sfn-state-machine" }
  )

  definition = <<EOF
{
  "Comment": "A state machine that generates invoices in docx and pdf files given a summarised data",
  "StartAt": "Insert to ExecutionStatus",
  "States": {
    "Insert to ExecutionStatus": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:putItem",
      "Parameters": {
        "TableName": "${data.terraform_remote_state.invoice_service.outputs.dynamodb_execution_status_table_name}",
        "Item": {
          "uuid": {
            "S.$": "$.uuid"
          },
          "account": {
            "S.$": "$.account"
          },
          "status": {
            "S": "started"
          },
          "execution": {
            "S.$": "$$.Execution.Id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "States.ALL"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "Next": "Invoice Generation",
      "ResultPath": null
    },
    "Invoice Generation": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload.$": "$",
        "FunctionName": "${aws_lambda_function.invoice_lambda.function_name}"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "Next": "Docx Generation Success?",
      "OutputPath": "$.Payload"
    },
    "Docx Generation Success?": {
      "Type": "Choice",
      "Choices": [
        {
          "Variable": "$.error",
          "IsPresent": true,
          "Next": "Invoice Generation = Failed"
        }
      ],
      "Default": "Invoice Generation = Success"
    },
    "Invoice Generation = Failed": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:putItem",
      "Parameters": {
        "TableName": "${data.terraform_remote_state.invoice_service.outputs.dynamodb_execution_status_table_name}",
        "Item": {
          "uuid": {
            "S.$": "$.uuid"
          },
          "account": {
            "S.$": "$.account"
          },
          "status": {
            "S": "invoice-generation-failed"
          },
          "bucket": {
            "S.$": "$.bucket"
          },
          "key": {
            "S.$": "$.key"
          },
          "readable_filename": {
            "S.$": "$.readable_filename"
          },
          "execution": {
            "S.$": "$$.Execution.Id"
          },
          "error":  {
            "S.$": "$.error"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "States.ALL"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "End": true
    },
    "Invoice Generation = Success": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:putItem",
      "Parameters": {
        "TableName": "${data.terraform_remote_state.invoice_service.outputs.dynamodb_execution_status_table_name}",
        "Item": {
          "uuid": {
            "S.$": "$.uuid"
          },
          "account": {
            "S.$": "$.account"
          },
          "status": {
            "S": "invoice-generation-success"
          },
          "bucket": {
            "S.$": "$.bucket"
          },
          "key": {
            "S.$": "$.key"
          },
          "readable_filename": {
            "S.$": "$.readable_filename"
          },
          "execution": {
            "S.$": "$$.Execution.Id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "States.ALL"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "Next": "PDF Generation",
      "ResultPath": null
    },
    "PDF Generation": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload.$": "$",
        "FunctionName": "${aws_lambda_function.pdf_lambda.function_name}"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "Next": "PDF Generation Success?",
      "OutputPath": "$.Payload"
    },
    "PDF Generation Success?": {
      "Type": "Choice",
      "Choices": [
        {
          "Variable": "$.error",
          "IsPresent": true,
          "Next": "PDF Generation = Failed"
        }
      ],
      "Default": "PDF Generation = Success"
    },
    "PDF Generation = Failed": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:putItem",
      "Parameters": {
        "TableName": "${data.terraform_remote_state.invoice_service.outputs.dynamodb_execution_status_table_name}",
        "Item": {
          "uuid": {
            "S.$": "$.uuid"
          },
          "account": {
            "S.$": "$.account"
          },
          "status": {
            "S": "pdf-generation-failed"
          },
          "bucket": {
            "S.$": "$.bucket"
          },
          "key": {
            "S.$": "$.key"
          },
          "readable_filename": {
            "S.$": "$.readable_filename"
          },
          "execution": {
            "S.$": "$$.Execution.Id"
          },
          "error":  {
            "S.$": "$.error.message"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "States.ALL"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "End": true
    },
    "PDF Generation = Success": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:putItem",
      "Parameters": {
        "TableName": "${data.terraform_remote_state.invoice_service.outputs.dynamodb_execution_status_table_name}",
        "Item": {
          "uuid": {
            "S.$": "$.uuid"
          },
          "account": {
            "S.$": "$.account"
          },
          "status": {
            "S": "pdf-generation-success"
          },
          "bucket": {
            "S.$": "$.bucket"
          },
          "key": {
            "S.$": "$.key"
          },
          "readable_filename": {
            "S.$": "$.readable_filename"
          },
          "execution": {
            "S.$": "$$.Execution.Id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "States.ALL"
          ],
          "IntervalSeconds": 2,
          "MaxAttempts": 6,
          "BackoffRate": 2
        }
      ],
      "End": true
    }
  }
}
EOF

}