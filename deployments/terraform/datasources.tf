locals {
  // ap-southeast-2 - AU
  // ap-southeast-1 - SG / MY
  // Networking Key
  networking_key       = var.aws_region == "ap-southeast-2" ? "networking/tfstate" : var.country == "sg" ? "networking/${var.aws_region}/tfstate" : "networking/${var.aws_region}/${var.country}/tfstate"
  production           = terraform.workspace == "prod"
  uat                  = terraform.workspace == "uat"
  mainline             = local.production || local.uat
  mainline_slug        = join("-", ["billing", var.project_name, terraform.workspace])
  slug                 = local.mainline ? lower(local.mainline_slug) : lower(join("-", [
    local.mainline_slug, var.commit_ref
  ]))
  prefix               = replace(replace(local.slug, "_", "-"), "--", "-")
  account_id           = data.aws_caller_identity.current.account_id
  ecr_repository_name  = "${local.prefix}-pdf-lambda-repo"
  ecr_image_tag        = "latest"
  billing_state_suffix = local.production ? "${var.aws_region}/${var.country}/tfstate" : local.uat ? "${var.aws_region}/${var.country}/main/tfstate" : "${var.aws_region}/${var.country}/${var.commit_ref}/tfstate"

  common_tags = module.required_tags.tags
}

data "aws_caller_identity" "current" {}

data "terraform_remote_state" "template_service" {
  workspace = terraform.workspace
  backend   = "s3"

  config = {
    bucket = var.remote_state_bucket
    region = var.remote_state_region
    key = "template-service/${local.billing_state_suffix}"
  }
}

data "terraform_remote_state" "invoice_service" {
  workspace = terraform.workspace
  backend   = "s3"

  config = {
    bucket = var.remote_state_bucket
    region = var.remote_state_region
    key = "invoice-service/${local.billing_state_suffix}"
  }
}

data "terraform_remote_state" "networking" {
  workspace = var.remote_workspace
  backend   = "s3"

  config = {
    bucket = var.remote_state_bucket
    region = var.remote_state_region
    key    = local.networking_key
  }
}