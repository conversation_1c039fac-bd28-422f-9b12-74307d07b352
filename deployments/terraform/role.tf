resource "aws_iam_role" "role" {
  name = replace("${substr(local.prefix, 0, 59)}-role", "--", "-")

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
POLICY
}

resource "aws_iam_role" "sfn_lambda_role" {
  name = replace("${substr(local.prefix, 0, 48)}-sfn-lambda-role", "--", "-")

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
POLICY
}

resource "aws_iam_policy" "lambda_policy" {
  name    = "${local.prefix}-lambda-policy"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "1",
            "Effect": "Allow",
            "Action": [ "ec2:CreateNetworkInterface",
                        "ec2:DescribeNetworkInterfaces",
                        "ec2:DeleteNetworkInterface"
                      ],
            "Resource": "*"
        },
        {
            "Sid": "2",
            "Effect": "Allow",
            "Action": [ "logs:CreateLogGroup",
                        "logs:CreateLogStream",
                        "logs:DescribeLogStreams",
                        "logs:GetLogEvents",
                        "logs:PutLogEvents",
                        "logs:PutRetentionPolicy"
                      ],
            "Resource": [
              "arn:aws:logs:*:*:log-group:/aws/lambda/${aws_lambda_function.invoice_lambda.function_name}:*",
              "arn:aws:logs:*:*:log-group:/aws/lambda/${aws_lambda_function.pdf_lambda.function_name}:*",
              "arn:aws:logs:*:*:log-group:/aws/lambda/${aws_lambda_function.sfn_lambda.function_name}:*"
            ]
        },
        {
            "Sid": "3",
            "Effect": "Allow",
            "Action": "s3:PutObject",
            "Resource": [
                "arn:aws:s3:::${data.terraform_remote_state.invoice_service.outputs.invoice_storage_bucket_name}/*",
                "arn:aws:s3:::${data.terraform_remote_state.invoice_service.outputs.invoice_storage_bucket_name}"
            ]
        },
        {
            "Sid": "4",
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::${data.terraform_remote_state.template_service.outputs.template_bucket_name}/*",
                "arn:aws:s3:::${data.terraform_remote_state.template_service.outputs.template_bucket_name}",
                "arn:aws:s3:::${data.terraform_remote_state.invoice_service.outputs.invoice_storage_bucket_name}/*",
                "arn:aws:s3:::${data.terraform_remote_state.invoice_service.outputs.invoice_storage_bucket_name}"
            ]
        }
    ]
}
EOF

}

resource "aws_iam_policy" "sfn_policy" {
  name    = "${local.prefix}-sfn-policy"
  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "1",
            "Effect": "Allow",
            "Action": [
                  "dynamodb:DescribeStream",
                  "dynamodb:GetRecords",
                  "dynamodb:GetShardIterator",
                  "dynamodb:ListStreams"
            ],
            "Resource": "${data.terraform_remote_state.invoice_service.outputs.dynamodb_summarised_table_arn}/*"
        },
        {
            "Sid": "2",
            "Effect": "Allow",
            "Action": [
                  "states:*"
            ],
            "Resource": "*"
        }
    ]
}
EOF

}

resource "aws_iam_role_policy_attachment" "lambda_policy_attach" {
  role      = aws_iam_role.role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

resource "aws_iam_role_policy_attachment" "step_function_lambda_policy_attach" {
  role      = aws_iam_role.sfn_lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

resource "aws_iam_role_policy_attachment" "step_function_sfn_policy_attach" {
  role      = aws_iam_role.sfn_lambda_role.name
  policy_arn = aws_iam_policy.sfn_policy.arn
}
