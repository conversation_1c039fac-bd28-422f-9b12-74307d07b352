#!/bin/bash

set -e

if [ -z "$ENVIRONMENT" ]; then
  echo "ENVIRONMENT variable must be set."
  exit 1
fi

export TF_VAR_country=$COUNTRY
echo "***** Running deploy job for $ENVIRONMENT environment and $REGION region and $COUNTRY country and $SERVICE_NAME service"
echo "***** Running terraform on ${TERRAFORM_WORKSPACE} environment"
cd deployments/terraform

if [[ "$ENVIRONMENT" =~ ^prod.* ]]; then
  # Production environment terraform state is stored at the top-level.
  export TERRAFORM_WORKSPACE_KEY_PREFIX="key=$SERVICE_NAME-service/${REGION}/${COUNTRY}/tfstate"
elif [[ "$ENVIRONMENT" =~ ^uat.* ]]; then
  # UAT environment terraform state was previously stored in the 'main' branch, as UAT deployments were triggered off merging to main.
  export TERRAFORM_WORKSPACE_KEY_PREFIX="key=$SERVICE_NAME-service/${REGION}/${COUNTRY}/main/tfstate"
elif [[ "$ENVIRONMENT" =~ ^review.* ]]; then
  # Review environment terraform state stored per feature branch.
  export TERRAFORM_WORKSPACE_KEY_PREFIX="key=$SERVICE_NAME-service/$REGION/${COUNTRY}/$CI_COMMIT_REF_SLUG/tfstate"
else
  echo "Unable to determine terraform workspace key prefix for environment: $ENVIRONMENT"
  exit 1
fi

echo "Using ${TERRAFORM_WORKSPACE_KEY_PREFIX}"

terraform init -backend-config="${TERRAFORM_WORKSPACE_KEY_PREFIX}"
terraform workspace select -or-create ${TERRAFORM_WORKSPACE}
terraform state replace-provider -auto-approve registry.terraform.io/-/aws registry.terraform.io/hashicorp/aws
terraform apply -auto-approve=true --var-file=variables/$REGION/$COUNTRY/${TERRAFORM_WORKSPACE}.tfvars
