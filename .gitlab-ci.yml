image: registry.gitlab.com/mnf-group/billing/base-image/vanilla-pipeline:latest

stages:
  - test
  - plan review
  - plan uat
  - plan production
  - deploy review
  - deploy uat
  - deploy production
  - destroy review
  - destroy uat
  - destroy production
  - update_badge
  - destroy

variables:
  GIT_DEPTH: 0
  DOCKER_DRIVER: overlay2
  DOCKER_HOST: tcp://localhost:2376/
  DOCKER_TLS_VERIFY: 1
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"

.common_variables: &common_variables
  SERVICE_NAME: "generation"
  TF_VAR_commit_ref: $CI_COMMIT_REF_SLUG
  TF_VAR_commit_tag: $CI_COMMIT_TAG
  ACCOUNT: $OIDC_ACCOUNT
  TEAM_ROLE_ARN: $OIDC_ROLE_default

.nonprod_variables: &nonprod_variables
  OIDC_ACCOUNT: "nonprod"
  <<: *common_variables

.prod_variables: &prod_variables
  OIDC_ACCOUNT: "prod"
  <<: *common_variables
  AWS_ACCOUNT_ID: ${prod_AWS_ACCOUNT_ID}

.my_variables: &my_variables
  REGION: ap-southeast-1
  TF_VAR_aws_region: $REGION
  AWS_DEFAULT_REGION: $REGION
  COUNTRY: my

.au_variables: &au_variables
  REGION: ap-southeast-2
  TF_VAR_aws_region: $REGION
  AWS_DEFAULT_REGION: $REGION
  COUNTRY: au

.review_deployment_variables: &review_deployment_variables
  TF_VAR_remote_workspace: uat
  TERRAFORM_WORKSPACE: dev

.uat_deployment_variables: &uat_deployment_variables
  TF_VAR_remote_workspace: uat
  TERRAFORM_WORKSPACE: uat

.prod_deployment_variables: &prod_deployment_variables
  TF_VAR_remote_workspace: prod
  TERRAFORM_WORKSPACE: prod

.common-tags: &common-tags
  tags: [ core-platform-runners, feature:dind]

.services-dind: &services-dind
  services:
    - $CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX/docker:27-dind

##################################################################################
# OIDC AWS ACCESS
##################################################################################
.execute_OIDC_aws_access:
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://gitlab.com
  before_script:
    - echo "machine gitlab.com login gitlab-ci-token password ${CI_JOB_TOKEN}" >> ${HOME}/.netrc
    - export ENVIRONMENT=${CI_ENVIRONMENT_SLUG}
    - |
      if [ -z "$TERRAFORM_WORKSPACE" ]; then
        export TERRAFORM_WORKSPACE=${ENVIRONMENT}
      fi
    - |
      if [ ! -z "$env" ]; then
        cp $env .env
      fi
    - echo ${ACCOUNT}
    - echo ${TEAM_ROLE_ARN}
    - unset AWS_ACCESS_KEY_ID
    - unset AWS_SECRET_ACCESS_KEY
    - grep -rl git::ssh://************** . | xargs sed -i "s|git::ssh://**************|git::https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com|g"
    - echo -e "\e[0Ksection_start:`date +%s`:my_first_section[collapsed=true]\r\e[0KIdentity Account section"
    - mkdir -p ~/.aws
    - echo "${GITLAB_OIDC_TOKEN}" > /tmp/web_identity_token
    - echo -e "[profile oidc]\nrole_arn=${TEAM_ROLE_ARN}\nweb_identity_token_file=/tmp/web_identity_token" > ~/.aws/config
    - export AWS_PROFILE=oidc
    - aws sts get-caller-identity
    - echo -e "\e[0Ksection_end:`date +%s`:my_first_section\r\e[0K"
    - echo -e "\e[0Ksection_start:`date +%s`:my_2nd_section[collapsed=true]\r\e[0KTarget Account section"
    - export ctrl="gitlab_oidc_role"
    - export role="\$${ctrl}_${ACCOUNT}"
    - eval echo $role > ~/.aws/config.sts_role
    - export sts_role=`cat ~/.aws/config.sts_role| tr -d '"'`
    - echo "${GITLAB_USER_ID}-${ACCOUNT}-${CI_PROJECT_ID}-${CI_PIPELINE_ID}-${CI_JOB_ID}@oidc" > ~/.aws/.cfg.sts_sn
    - export sts_sn=`cat ~/.aws/.cfg.sts_sn| tr -d '"'`
    - aws sts assume-role --role-arn $sts_role --role-session-name $sts_sn > ~/aws_session_creds.txt
    - export AWS_ACCESS_KEY_ID=$(cat ~/aws_session_creds.txt | jq .Credentials.AccessKeyId | xargs)
    - export AWS_SECRET_ACCESS_KEY=$(cat ~/aws_session_creds.txt | jq .Credentials.SecretAccessKey | xargs)
    - export AWS_SESSION_TOKEN=$(cat ~/aws_session_creds.txt | jq .Credentials.SessionToken | xargs)
    - aws sts get-caller-identity
    - echo -e "\e[0Ksection_end:`date +%s`:my_2nd_section\r\e[0K"
    - echo "assume role in $ACCOUNT with session name - $sts_sn"

.plan_deployment: &plan_deployment
  script:
    - sh deployments/plan.sh

.apply_deployment: &apply_deployment
  script:
    - sh deployments/deploy.sh

.destroy_deployment: &destroy_deployment
  script:
    - sh deployments/destroy.sh

##################################################################################
# Test
##################################################################################
.base_test: &base_test
  stage: test
  needs: []
  only: [main, merge_requests]
  variables:
    <<: *common_variables
    <<: *my_variables
  <<: *common-tags

.base_python_test: &base_python_test
  extends: .base_test
  image: python:alpine
  script:
    - pip3 install -r requirements.txt
    - pip3 install coverage
    - coverage run --omit=test_app.py -m unittest discover
    - coverage report
    - coverage html
  coverage: '/TOTAL.*\s+(\d+%)/'

.base_javascript_test: &base_javascript_test
  extends: .base_test
  image: node:alpine
  script:
    - npm install
    - npx jest --coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'

"test: [invoice generation]":
  extends: .base_python_test
  before_script:
    - cd invoice-generation/app

"test: [pdf generation]":
  extends: .base_javascript_test
  before_script:
    - cd pdf-generation/app

"test: [step function]":
  extends: .base_python_test
  before_script:
    - cd step-function/app

##################################################################################
# Update Gitlab Badges
##################################################################################
#.base_update_badges: &base_update_badges
#  stage: update_badge
#  needs: ["deploy: [production]"]
#  only: [main]
#  <<: *common-tags
#
#.base_update_lambda_badge: &base_update_lambda_badge
#  extends: .base_update_badges
#  variables:
#    <<: *prod_variables
#  script:
#    - aws configure set aws_access_key_id ${AWS_ACCESS_KEY_ID}
#    - aws configure set aws_secret_access_key ${AWS_SECRET_ACCESS_KEY}
#    - aws configure set region ${REGION}
#
#"update badge: [terraform]":
#  extends: .base_update_badges
#  script:
#    - TF_VERSION=$(terraform -v | { read _ v; echo ${v#v}; })
#    - TERRAFORM_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("terraform")).id')
#    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Terraform-${TF_VERSION}-844FBA?logo=terraform" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${TERRAFORM_BADGE_ID}
#
#"update badge: [invoice generation python]":
#  extends: .base_update_lambda_badge
#  script:
#    - INVOICE_LAMBDA_VERSION=$(aws lambda get-function --function-name arn:aws:lambda:${REGION}:${AWS_ACCOUNT_ID}:function:billing-generation-prod-my-invoice-lambda --query 'Configuration.Runtime' | sed 's/[^0-9.]*//g')
#    - PYTHON_INVOICE_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("python_invoice_lambda")).id')
#    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Python Invoice Lambda-${INVOICE_LAMBDA_VERSION}-3776AB?logo=python" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${PYTHON_INVOICE_BADGE_ID}
#
#"update badge: [step function python]":
#  extends: .base_update_lambda_badge
#  script:
#    - SFN_LAMBDA_VERSION=$(aws lambda get-function --function-name arn:aws:lambda:${REGION}:${AWS_ACCOUNT_ID}:function:billing-generation-prod-my-sfn-lambda --query 'Configuration.Runtime' | sed 's/[^0-9.]*//g')
#    - PYTHON_SFN_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("python_sfn_lambda")).id')
#    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Python SFN Lambda-${SFN_LAMBDA_VERSION}-3776AB?logo=python" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${PYTHON_SFN_BADGE_ID}
#
#"update badge: [pdf generation node]":
#  extends: .base_update_lambda_badge
#  <<: *services-dind
#  script:
#    - aws ecr get-login-password --region ${REGION} | docker login --username AWS --password-stdin https://${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com
#    - docker pull ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/billing-generation-prod-my-pdf-lambda-repo:latest
#    - docker run --rm -d ${AWS_ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/billing-generation-prod-my-pdf-lambda-repo:latest
#    - PDF_LAMBDA_NODE_VERSION=$(docker exec $(docker ps -q -n 1) node -v | sed 's/[^0-9.]*//g')
#    - docker logout billing-generation-prod-my-pdf-lambda-repo
#    - NODE_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("node.js")).id')
#    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Node.js-${PDF_LAMBDA_NODE_VERSION}-339933?logo=node.js" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${NODE_BADGE_ID}

##################################################################################
# Review
##################################################################################
.plan:
  extends: .execute_OIDC_aws_access
  stage: plan
  <<: *plan_deployment
  needs: []
  <<: *common-tags

.plan_review:
  stage: plan review
  extends: .plan
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  variables:
    <<: *nonprod_variables
    TF_VAR_remote_workspace: uat
    TERRAFORM_WORKSPACE: dev
  only: [ merge_requests ]
  when: on_success

.plan_uat:
  stage: plan uat
  extends: .plan
  environment:
    name: uat-$COUNTRY
    deployment_tier: staging
  variables:
    <<: *nonprod_variables
    TF_VAR_remote_workspace: uat
    TERRAFORM_WORKSPACE: uat
  only: [ main, tags ]

.plan_production:
  stage: plan production
  extends: .plan
  environment:
    name: prod-$COUNTRY
    deployment_tier: production
  variables:
    <<: *prod_variables
    TF_VAR_remote_workspace: prod
    TERRAFORM_WORKSPACE: prod
  only: [ main ]

"plan my: [review]":
  extends: .plan_review
  variables:
    <<: *my_variables

"plan my: [uat]":
  extends: .plan_uat
  variables:
    <<: *my_variables

"plan my: [production]":
  extends: .plan_production
  variables:
    <<: *my_variables

"plan au: [review]":
  extends: .plan_review
  variables:
    <<: *au_variables

"plan au: [uat]":
  extends: .plan_uat
  variables:
    <<: *au_variables

"plan au: [production]":
  extends: .plan_production
  variables:
    <<: *au_variables

##################################################################################
# Deploy
##################################################################################
.deploy:
  extends: .execute_OIDC_aws_access
  stage: deploy
  variables:
    DOCKER_IMAGE: $REGISTRY/$SERVICE_NAME:$CI_COMMIT_SHA
  <<: *services-dind
  <<: *apply_deployment
  <<: *common-tags
  when: manual

.deploy_review:
  stage: deploy review
  extends: .deploy
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  variables:
    <<: *nonprod_variables
    <<: *review_deployment_variables
  only: [ merge_requests ]

.deploy_uat:
  stage: deploy uat
  extends: .deploy
  environment:
    name: uat-$COUNTRY
    deployment_tier: staging
  variables:
    <<: *nonprod_variables
    <<: *uat_deployment_variables
  only: [ main, tags ]

.deploy_production:
  stage: deploy production
  extends: .deploy
  environment:
    name: prod-$COUNTRY
    deployment_tier: production
  variables:
    <<: *prod_variables
    <<: *prod_deployment_variables
  only: [ main ]

"deploy my: [review]":
  extends: .deploy_review
  variables:
    <<: *my_variables
  needs: ["plan my: [review]"]

"deploy my: [uat]":
  extends: .deploy_uat
  variables:
    <<: *my_variables
  needs: ["plan my: [uat]"]

"deploy my: [production]":
  extends: .deploy_production
  variables:
    <<: *my_variables
  needs: ["plan my: [production]"]

"deploy au: [review]":
  extends: .deploy_review
  variables:
    <<: *au_variables
  needs: ["plan au: [review]"]

"deploy au: [uat]":
  extends: .deploy_uat
  variables:
    <<: *au_variables
  needs: ["plan au: [uat]"]

"deploy au: [production]":
  extends: .deploy_production
  variables:
    <<: *au_variables
  needs: ["plan au: [production]"]

##################################################################################
# Destroy
##################################################################################
.destroy:
  extends: .execute_OIDC_aws_access
  stage: destroy
  environment:
    action: stop
  needs: []
  when: manual
  <<: *destroy_deployment
  <<: *common-tags

.destroy_review:
  stage: destroy review
  extends: .destroy
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  variables:
    <<: *nonprod_variables
    TF_VAR_remote_workspace: uat
    TERRAFORM_WORKSPACE: dev
  only: [ merge_requests ]

.destroy_uat:
  stage: destroy uat
  extends: .destroy
  environment:
    name: uat-$COUNTRY
    deployment_tier: staging
  variables:
    <<: *nonprod_variables
    TF_VAR_remote_workspace: uat
    TERRAFORM_WORKSPACE: uat
  only: [ main, tags ]

.destroy_production:
  stage: destroy production
  extends: .destroy
  environment:
    name: prod-$COUNTRY
    deployment_tier: production
  variables:
    <<: *prod_variables
    TF_VAR_remote_workspace: prod
    TERRAFORM_WORKSPACE: prod
  only: [ main ]

"destroy my: [review]":
  extends: .destroy_review
  variables:
    <<: *my_variables

"destroy my: [uat]":
  extends: .destroy_uat
  variables:
    <<: *my_variables

"destroy my: [production]":
  extends: .destroy_production
  variables:
    <<: *my_variables

"destroy au: [review]":
  extends: .destroy_review
  variables:
    <<: *au_variables

"destroy au: [uat]":
  extends: .destroy_uat
  variables:
    <<: *au_variables

"destroy au: [production]":
  extends: .destroy_production
  variables:
    <<: *au_variables
