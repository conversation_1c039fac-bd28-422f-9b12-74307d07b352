import unittest
from app import (
    deserialize_dynamo_to_python,
    serialize_python_to_dynamo
)


class TestDynamodbObjectConversion(unittest.TestCase):

    def test_deserialize_dynamo_to_python(self):
        self.assertDictEqual(deserialize_dynamo_to_python(get_dynamodb_data()), get_python_data())

    def test_serialize_dynamo_to_python(self):
        self.assertDictEqual(serialize_python_to_dynamo(get_python_data()), get_dynamodb_data())

def get_dynamodb_data():
    return {
        "uuid": {
            "S": "**************"
        },
        "name": {
            "S": "DIDWW Ireland Ltd."
        },
        "account_number": {
            "S": "**************"
        },
        "billing_address": {
            "S": "01/"
        },
        "new_charges_summary_rows": {
            "L": [
                {
                    "M": {
                        "description": {
                            "S": "Service Charges"
                        },
                        "ex_gst": {
                            "S": "300.00"
                        },
                        "gst": {
                            "S": "21.00"
                        },
                        "inc_gst": {
                            "S": "321.00"
                        }
                    }
                }
            ]
        },
    }


def get_python_data():
    return {
        'uuid': '**************',
        'name': 'DIDWW Ireland Ltd.',
        'account_number': '**************',
        'billing_address': '01/',
        'new_charges_summary_rows': [
            {
                'description': 'Service Charges',
                'ex_gst': '300.00',
                'gst': '21.00',
                'inc_gst': '321.00'
            }
        ]
    }


if __name__ == '__main__':
    unittest.main()
