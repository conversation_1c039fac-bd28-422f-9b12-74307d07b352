import os
import traceback
from io import BytesIO

import boto3
import jinja2
from docxtpl import DocxT<PERSON>plate


def handler(event, _):
    body = event
    body['lambda'] = 'invoice-generation'
    body['key'] = ''

    try:
        print(f'Received event: {event}')

        s3 = boto3.client('s3')
        jinja_env = build_jinja_env()
        template_bucket = os.environ['TEMPLATE_BUCKET_NAME']
        invoice_storage_bucket = os.environ['INVOICE_STORAGE_BUCKET_NAME']
        body['bucket'] = invoice_storage_bucket

        dynamo_record = event['record']
        context = deserialize_dynamo_to_python(dynamo_record)
        template_key = context['template_path']
        folder = context['account_id']

        # use only the filename as the outputpath (do not include the subfolders)
        filename = template_key.split("/")[-1]
        outpath = os.path.join('/tmp', filename)
        filename = context['uuid'] + '.docx'
        docxFile = folder + "/" + filename
        body['key'] = docxFile
        body['readable_filename'] = context['filename']

        # download template from s3 template bucket into /tmp/<filename>
        print(f'downloading template: {template_key} from bucket: {template_bucket} into path: {outpath}')
        s3.download_file(template_bucket, template_key, outpath)
        doc = DocxTemplate(outpath)

        # Renders context variables into docx template
        doc.render(context, jinja_env)
        print(f'document rendered successfully in {outpath}')

        # Save rendered document to invoice generation s3 bucket
        with BytesIO() as docx_stream:
            doc.save(docx_stream)
            docx_stream.seek(0)
            s3.upload_fileobj(docx_stream, invoice_storage_bucket, docxFile)
            print(f'document uploaded to s3 bucket: {invoice_storage_bucket} with filename: {docxFile}')

    except Exception as e:
        print(f'An exception occurred: {e}')
        # printing stack trace
        traceback.print_exc()
        # do not change key 'error' as the existence of this key is used as a filter in the state machine
        body['error'] = str(e)

    return body


# handles translation from dynamo record to ordinary json key: value structure
def deserialize_dynamo_to_python(low_level_data):
    # Lazy-eval the dynamodb attribute (boto3 is dynamic!)
    boto3.resource('dynamodb')

    # To go from low-level format to python
    deserializer = boto3.dynamodb.types.TypeDeserializer()
    python_data = {k: deserializer.deserialize(v) for k, v in low_level_data.items()}
    return python_data


def serialize_python_to_dynamo(python_data):
    # Lazy-eval the dynamodb attribute (boto3 is dynamic!)
    boto3.resource('dynamodb')

    # To go from python to low-level format
    serializer = boto3.dynamodb.types.TypeSerializer()
    low_level_copy = {k: serializer.serialize(v) for k, v in python_data.items()}
    return low_level_copy


def build_jinja_env():
    jinja_env = jinja2.Environment()
    jinja_env.filters['null_check'] = null_check
    jinja_env.filters['sign_check'] = sign_check
    jinja_env.filters['abs_check'] = abs_check
    jinja_env.filters['format_amount'] = format_amount
    return jinja_env


###################################################################
#                     CUSTOM JINJA2 FILTERS
###################################################################
def null_check(value):
    return "N/A" if value is None else value


def sign_check(value):
    try:
        value = float(value.replace(',', ''))
        if value >= 0:
            return True
        else:
            return False
    except ValueError:
        raise


def abs_check(value):
    try:
        value = float(value.replace(',', ''))
        value = abs(value)
        return "{:,.2f}".format(value)
    except ValueError:
        raise


def format_amount(value):
    try:
        positive = sign_check(value)
        if positive:
            return value
        else:
            return "(" + abs_check(value) + ")"
    except ValueError:
        raise


def get_latest_template(bucket, subfolder):
    get_last_modified = lambda obj: int(obj['LastModified'].strftime('%s'))

    s3 = boto3.client('s3')
    objs = s3.list_objects_v2(Bucket=bucket, Prefix=subfolder)['Contents']
    last_added = [obj['Key'] for obj in sorted(objs, key=get_last_modified, reverse=True)][0]
    return last_added


if __name__ == '__main__':
    handler("", "")
