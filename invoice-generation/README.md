# Invoice Generation

A lambda service that converts ingests invoice enrichment data from dynamodb and renders an invoice in docx format. It will use docx templates hosted on S3 buckets, created by template service in the billing project. 

## Prerequisites

* [Python](https://www.python.org/)
* [An AWS account](https://aws.amazon.com/)

### Setup for Windows
```shell
# Initialise virtual env
pip3 install virtualenv
virtualenv --python=python3 venv
# Windows
source venv/Scripts/activate
# Linux
source venv/bin/activate

# Install requirements
pip install -r app/requirements.txt
```

### Testing
```shell
cd app
python -m unittest test_app
```

## Triggers

### Automatic
Triggered by dynamo event streams from insertion into the summarised model table in invoice service.

### Sample Test Event
```json
{
    "Records": [
        {
            "eventID": "36d424b86161e80bcba2aa6353b24cfe",
            "eventName": "INSERT",
            "eventVersion": "1.1",
            "eventSource": "aws:dynamodb",
            "awsRegion": "ap-southeast-1",
            "dynamodb": {
                "ApproximateCreationDateTime": **********.0,
                "Keys": {
                    "uuid": {
                        "S": "dabf84d1-ac32-11ed-9e0e-895d43d8ec4a"
                    }
                },
                "NewImage": {
                    "uuid": {
                        "S": "dabf84d1-ac32-11ed-9e0e-895d43d8ec4a"
                    },
                    "name": {
                        "S": "Symbio Test"
                    },
                    "account_number": {
                        "S": "54321"
                    },
                    "invoice_number": {
                        "S": "INV0002"
                    }
                },
                "SequenceNumber": "14979400000000001214991216",
                "SizeBytes": 801,
                "StreamViewType": "NEW_IMAGE"
            },
            "eventSourceARN": "arn:aws:dynamodb:ap-southeast-1: ************:table/billing-invoice-service-uat-prgmly-516-SummarisedModel/stream/2023-02-16T21: 44: 53.521"
        }
    ]
}
```

### Rebuilding docxtpl-layer.zip in layers directory
For instructions to rebuild the layer visit: https://mnfgroup.atlassian.net/wiki/spaces/BLG/pages/***********/SPIKE+PRGMLY-856+Jinja+Python+templating

Or do the following:

- Create directory `docxtpl` in local
- Inside the folder, create a `requirements.txt` file with text `docxtpl`
- In the terminal, execute the following:
```shell
# Create layer for python3.12
> docker run -v "%cd%":/var/task "public.ecr.aws/sam/build-python3.12" /bin/sh -c "pip install -r requirements.txt -t python/lib/python3.12/site-packages/; exit"

# Zip layer
> 7z a -tzip docxtpl-layer.zip python
```
- Replace the layer in the layers folder